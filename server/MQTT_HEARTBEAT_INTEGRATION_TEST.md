# MQTT心跳功能集成测试指南

## 集成完成情况

✅ **已完成的集成工作**：

1. **配置文件集成** - 在 `conf/config.ini` 中添加了 `[mqtt_heartbeat]` 配置段
2. **主程序集成** - 在 `src/common/main.cpp` 中集成了心跳功能
3. **构建系统集成** - 更新了 `CMakeLists.txt` 链接heartbeat库
4. **编译验证** - 成功编译生成 `data_capture_server` 可执行文件

## 配置说明

在 `conf/config.ini` 中的心跳配置：

```ini
[mqtt_heartbeat]
; MQTT心跳配置
enable = 1                          ; 1=启用, 0=禁用
server_host = localhost             ; MQTT服务器地址
server_port = 1883                  ; MQTT服务器端口
username =                          ; MQTT用户名（可选）
password =                          ; MQTT密码（可选）
device_id = data_capture_device_001 ; 设备唯一标识
topic = heartbeat/data_capture      ; 心跳发布主题
heartbeat_interval_sec = 10         ; 心跳间隔（秒）
enable_tls = 0                      ; TLS加密（预留，暂不支持）
```

## 测试步骤

### 1. 准备MQTT服务器

#### 选项A：使用Docker启动Mosquitto
```bash
# 启动MQTT服务器
docker run -it -p 1883:1883 eclipse-mosquitto:latest

# 或者后台运行
docker run -d -p 1883:1883 --name mqtt-server eclipse-mosquitto:latest
```

#### 选项B：安装本地Mosquitto
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install mosquitto mosquitto-clients

# 启动服务
sudo systemctl start mosquitto
sudo systemctl enable mosquitto
```

### 2. 订阅心跳主题（用于监控）

在另一个终端中订阅心跳主题：
```bash
# 使用mosquitto_sub监听心跳消息
mosquitto_sub -h localhost -p 1883 -t "heartbeat/data_capture" -v

# 如果有用户名密码
mosquitto_sub -h localhost -p 1883 -u username -P password -t "heartbeat/data_capture" -v
```

### 3. 运行数据采集服务器

```bash
cd /home/<USER>/data/code/raw_data_capture_software/server
./bin/data_capture_server
```

### 4. 预期结果

#### 启动时的日志输出：
```
INFO: MQTT heartbeat service started successfully
```

#### 心跳消息格式：
每10秒会在MQTT主题 `heartbeat/data_capture` 上收到如下格式的消息：
```json
{
  "timestamp": 1704614400000,
  "device_id": "data_capture_device_001"
}
```

#### 连接失败时的日志：
```
ERROR: HeartbeatSender: Failed to connect to MQTT server
INFO: HeartbeatSender: Attempting to connect to MQTT server...
```

### 5. 测试场景

#### 场景1：正常运行测试
- 启动MQTT服务器
- 启动数据采集服务器
- 观察心跳消息是否每10秒发送一次

#### 场景2：网络中断恢复测试
- 正常运行后，停止MQTT服务器
- 观察错误日志和重连尝试
- 重新启动MQTT服务器
- 验证心跳服务是否自动恢复

#### 场景3：配置禁用测试
- 修改配置文件 `enable = 0`
- 重启服务器
- 验证心跳服务不启动

## 故障排除

### 问题1：连接失败
**现象**：日志显示连接MQTT服务器失败
**解决**：
- 检查MQTT服务器是否运行：`netstat -an | grep 1883`
- 检查防火墙设置
- 验证服务器地址和端口配置

### 问题2：心跳消息未发送
**现象**：连接成功但没有心跳消息
**解决**：
- 检查主题权限
- 验证设备ID和主题配置
- 查看详细错误日志

### 问题3：程序启动失败
**现象**：程序无法启动或立即退出
**解决**：
- 检查配置文件路径和格式
- 验证所有依赖库是否正确链接
- 查看启动日志中的错误信息

## 配置调优建议

1. **心跳间隔**：根据网络条件和业务需求调整，建议5-60秒
2. **设备ID**：使用唯一标识，建议包含MAC地址或序列号
3. **主题设计**：使用层次化主题，如 `heartbeat/location/device_id`
4. **认证**：生产环境建议启用用户名密码认证

## 监控建议

1. **MQTT客户端工具**：使用MQTT Explorer等图形化工具监控
2. **日志监控**：监控应用日志中的心跳相关信息
3. **网络监控**：监控MQTT服务器的连接状态
4. **告警设置**：设置心跳超时告警机制
