#include "data_capture_manager.h"
#include "vector"
#include "commonFunction.h"

CDataCaptureManager::CDataCaptureManager(CMediator *pMediator)
    : CModuleBase(pMediator)
{
    m_dwMaxConnect = 30;
}

CDataCaptureManager::~CDataCaptureManager()
{
}
void CDataCaptureManager::Init()
{
    // 读配置文件
    readConfig();
}
void CDataCaptureManager::Start()
{
    if(m_bIsRunning)
    {
        return;
    }

    //创建实例
    for(auto iter:m_vecConnectArgs)
    {
        if(iter.bEnable == false)
        {
            continue;
        }
        std::cout << "argsType " << iter.argsType << std::endl;
        switch (iter.argsType)
        {
        case TConnectArgs::MQTT:
        {
            // CCaptureBase *pHandle = new CCaptureMqtt(*this);
            // pHandle->SetConfig(iter);
            CCaptureBase *pHandle = new CCaptureMqttCpp(*this);
            pHandle->SetConfig(iter);
            m_vecHandle.push_back(pHandle); 
        
        }
        break;
        case TConnectArgs::KAFKA:
        {
            CCaptureBase *pHandle = new CCaptureKafka(*this);
            pHandle->SetConfig(iter);
            m_vecHandle.push_back(pHandle);
            std::cout << "this  is KAFKA server"  << std::endl;
            
        }
        break;
        case TConnectArgs::TCPSERVER:
        {
            CCaptureBase *pHandle = new CCaptureTcpServer(*this);
            pHandle->SetConfig(iter);
            m_vecHandle.push_back(pHandle);
            std::cout << "this  is tcp server"  << std::endl;
        }
        break;
        case TConnectArgs::UDP:
        {
            CCaptureBase *pHandle = new CCaptureUdp(*this);
            pHandle->SetConfig(iter);
            m_vecHandle.push_back(pHandle);
            std::cout << "this  is udp server"  << std::endl;
        }
        break;
        default:
        std::cout << "this  is none server"  << std::endl;
            break;
        }

        // 添加状态信息到vector中
        TCapStatusUpdate stStatus{};
        stStatus.strCrossroadId = iter.strRoadId;
        stStatus.strDescribe = iter.strDecribe;
        m_vecStatus.push_back(stStatus);

    }

    //start
    for(auto iter: m_vecHandle)
    {
        // 设置回调
        std::function<void(TRawData stCapData)> cb = [this](TRawData stCapData)
        {
            this->dataRecv(stCapData);
        };
        iter->SetCallBack(cb);
        // start
        iter->Start();
        sleep(1);

    }
    m_bIsRunning = true;

    //创建发送实例
    m_sendThread = std::thread(&CDataCaptureManager::sendStatus,this,commonFunc::GetMsTime());

}
void CDataCaptureManager::Stop()
{
    m_bIsRunning = false;
    updateConfig();

    for (auto iter = m_vecHandle.begin(); iter != m_vecHandle.end();) {
        if (*iter)
        {                    // 检查指针有效性
            (*iter)->Stop(); // 停止处理
            if (*iter)
                delete *iter;    // 删除指针指向的对象
        }
        iter = m_vecHandle.erase(iter); // 删除元素，并将迭代器指向下一个位置
    }
    m_vecStatusMutex.lock();
    for (auto iter = m_vecStatus.begin();iter != m_vecStatus.end();)
    {
        iter = m_vecStatus.erase(iter);
    }
    m_vecStatusMutex.unlock();
   
    if(m_sendThread.joinable())
    {
        m_sendThread.join();
    }
    
}
void CDataCaptureManager::Pause()
{
}
bool CDataCaptureManager::MsgFilter(u32 msgType)
{
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE ||
        msgType == CAPTURE_CONFIG_UPDATE ||
        msgType == CAPTURE_CONFIG_REQ)
    {
        ret = false;
    }
    return ret;
}

void CDataCaptureManager::HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType, std::shared_ptr<void> spData)
{
    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    case CAPTURE_CONFIG_UPDATE:
        parseConfigFromMsg(msgLevel,deviceId,spData);
        break;
    case CAPTURE_CONFIG_REQ:
        configReq(msgLevel,deviceId,spData);
        break;
    default:
        break;
    }
}


bool CDataCaptureManager::updateConfig()
{
    int dwTotalCnt = m_vecConnectArgs.size();
    if(m_config.Move2Section("common"))
    {
        m_config.WriteKey("totalNum",std::to_string(dwTotalCnt));
    }
    auto iter = m_vecConnectArgs.begin();
    for(int i=0;i<m_dwMaxConnect;i++)
    {
        std::string strSection = "connect" + std::to_string(i);
        m_config.WriteKey("id",std::to_string(i));
        if(!m_config.Move2Section(strSection))
        {
            continue;
        }
        if(iter !=m_vecConnectArgs.end())
        {
            m_config.WriteKey("enable",iter->bEnable);
            m_config.WriteKey("roadId",iter->strRoadId);
            m_config.WriteKey("topic",iter->strTopic);
            m_config.WriteKey("username",iter->strUsername);
            m_config.WriteKey("password",iter->strPassword);
            m_config.WriteKey("addr",iter->strAddr);
            m_config.WriteKey("clientId", iter->strClientId);
            m_config.WriteKey("describe",iter->strDecribe);
            m_config.WriteKey("factory",iter->dwFactory);
            switch (iter->argsType)
            {
            case TConnectArgs::MQTT:
                m_config.WriteKey("type", "mqtt");
                break;

            case TConnectArgs::HTTP:
                m_config.WriteKey("type", "http");
                break;
            case TConnectArgs::KAFKA:
                m_config.WriteKey("type", "kafka");
                break;
            case TConnectArgs::TCPSERVER:
                m_config.WriteKey("type", "tcp_server");
                break;
            case TConnectArgs::UDP:
                m_config.WriteKey("type", "udp");
                break;
            default:
                break;
            }
            iter++;
        }else{
            m_config.WriteKey("enable","0");
        }
    }
    m_config.Save2File();

    return true;
}

void CDataCaptureManager::dataRecv(TRawData stCapData)
{
    m_mutex.lock();
    std::shared_ptr<TRawData> spData = std::make_shared<TRawData>();
    spData->llDataTime = stCapData.llDataTime;
    spData->llRecvTime = stCapData.llRecvTime;
    spData->strCrossroadId = stCapData.strCrossroadId;
    spData->strData = stCapData.strData;
    spData->wDataLength = stCapData.wDataLength;
    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,CAPTURE_DATA_UPDATE,spData);
    m_mutex.unlock();
}

void CDataCaptureManager::updateStatus(std::string strId, bool isCap,float fFreq,u32 recvDataCnt)
{
    std::lock_guard<std::mutex> lock(m_vecStatusMutex);  // 自动管理锁定与解锁
    for(auto &it:m_vecStatus)
    {
       if(it.strCrossroadId == strId)
       {
            it.bIsCaptruing = isCap;
            it.llTimestamp = commonFunc::GetMsTime();
            it.fFreq = fFreq;
            it.dwRecvDataCnt = recvDataCnt;
       }
    }
}


bool CDataCaptureManager::parseConfigFromMsg(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::shared_ptr<TArgsList> pList = std::static_pointer_cast<TArgsList>(spData);
    if(!pList)
    {
        return false;
    }   


    if(pList->vecList.size() == 0)
    {
        return false;
    }

    // //清空
    std::vector<TConnectArgs>().swap(m_vecConnectArgs);
    // m_vecConnectArgs.clear();

    //填充新数据
    for(auto iter: pList->vecList)
    {
        m_vecConnectArgs.push_back(iter);
    //test
        std::cout << "dwCnt " << iter.dwCnt << std::endl;
        std::cout << "bEnable " << iter.bEnable << std::endl;
        std::cout << "argsType " << iter.argsType << std::endl;
        std::cout << "strRoadId " << iter.strRoadId << std::endl;
        std::cout << "strTopic " << iter.strTopic << std::endl;
        std::cout << "strUsername " << iter.strUsername << std::endl;
        std::cout << "strPassword " << iter.strPassword << std::endl;
        std::cout << "strClientId " << iter.strClientId << std::endl;
        std::cout << "strAddr " << iter.strAddr << std::endl;
        std::cout << "strDecribe " << iter.strDecribe << std::endl;

    }

    

    // Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,CAPTURE_CONFIG_RES,std::static_pointer_cast<void>(pList));

    return true;
}

bool CDataCaptureManager::configReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{

    std::shared_ptr<TArgsList> pList = std::static_pointer_cast<TArgsList>(spData);
    if(!pList)
    {
        return false;
    }

    pList->dwCnt = m_vecConnectArgs.size();
    for(auto iter : m_vecConnectArgs)
    {
        pList->vecList.push_back(iter);
    }

    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,CAPTURE_CONFIG_RES,std::static_pointer_cast<void>(pList));

    return true;

}

bool CDataCaptureManager::readConfig()
{

    if (!m_config.OpenConfig("../conf/capture_config.ini"))
    {
        return false;
    }

    if (!m_config.Move2Section("common"))
    {
        ERROR("Config file can not find section common.");
        return false;
    }

    int totalConnectCnt = std::stoi(m_config.GetValueByKey("totalNum","0"));
    //读取参数
    for(int i=0;i<m_dwMaxConnect;i++)
    {
        std::string strSectionName = "connect" + std::to_string(i);
        if(!m_config.Move2Section(strSectionName))
        {
            continue;
        }
        TConnectArgs stConnectArgs;
        stConnectArgs.dwCnt = std::stoi(m_config.GetValueByKey("id","0"));
        stConnectArgs.bEnable = std::stoi(m_config.GetValueByKey("enable","0"));
        stConnectArgs.strRoadId = m_config.GetValueByKey("roadId","");
        stConnectArgs.strTopic = m_config.GetValueByKey("topic","");
        stConnectArgs.strUsername = m_config.GetValueByKey("username","");
        stConnectArgs.strPassword = m_config.GetValueByKey("password","");
        stConnectArgs.strAddr = m_config.GetValueByKey("addr","");
        stConnectArgs.strClientId = m_config.GetValueByKey("clientId","");
        stConnectArgs.strDecribe = m_config.GetValueByKey("describe","");
        stConnectArgs.dwFactory = std::stoi(m_config.GetValueByKey("factory","0"));
        std::string strCaptureType = m_config.GetValueByKey("type", "");
        if (strCaptureType == "mqtt")
        {
            stConnectArgs.argsType = TConnectArgs::MQTT;
        }
        else if (strCaptureType == "http")
        {
            stConnectArgs.argsType = TConnectArgs::HTTP;
        }else if (strCaptureType == "kafka")
        {
            stConnectArgs.argsType = TConnectArgs::KAFKA;
        }else if (strCaptureType == "tcp_server")
        {
            stConnectArgs.argsType = TConnectArgs::TCPSERVER;
        }else if (strCaptureType == "udp")
        {
            stConnectArgs.argsType = TConnectArgs::UDP;
        }

        // std::cout << "id = " << stConnectArgs.dwCnt ; 
        // std::cout << "enable = " << stConnectArgs.bEnable ; 
        // std::cout << "roadId = " << stConnectArgs.strRoadId ; 
        // std::cout << "topic = " << stConnectArgs.strTopic ; 
        // std::cout << "username = " << stConnectArgs.strUsername ; 
        // std::cout << "password = " << stConnectArgs.strPassword ; 
        // std::cout << "addr = " << stConnectArgs.strAddr ; 
        // std::cout << "clientId = " << stConnectArgs.strClientId ; 
        // std::cout << std::endl;

        // if(stConnectArgs.bEnable)
        {
            m_vecConnectArgs.push_back(stConnectArgs);
        }
    }

    std::shared_ptr<TArgsList> spList = std::make_shared<TArgsList>();
    spList->dwCnt = m_vecConnectArgs.size();
    for(auto iter :m_vecConnectArgs)
    {
        spList->vecList.push_back(iter);
    }

    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,CAPTURE_CONFIG_UPDATE,std::static_pointer_cast<void>(spList));

    return true;
}


void CDataCaptureManager::sendStatus(u64 startTime)
{
    while(m_bIsRunning)
    {
        std::shared_ptr<TMonitor> spMonitor = std::make_shared<TMonitor>();
        spMonitor->llUpdateTime = commonFunc::GetMsTime();
        spMonitor->dwStatusCnt = m_vecStatus.size();
        spMonitor->llStartTime = startTime;
        spMonitor->llDurationTime = spMonitor->llUpdateTime - startTime;
        m_vecStatusMutex.lock();
        for(auto iter:m_vecStatus)
        {
            spMonitor->vecStatusList.push_back(iter);
        }
        m_vecStatusMutex.unlock();
        Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,CAPTURE_MONITOR_UPDATE,std::static_pointer_cast<void>(spMonitor));
        sleep(2);
    }
}