cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)

find_package(OpenSSL REQUIRED)

add_library(heartbeat SHARED ${DIR_SRC})
target_link_libraries(heartbeat  ssl crypto )

add_custom_command(TARGET heartbeat POST_BUILD
 COMMAND
 mv libheartbeat.so libheartbeat.so.${PROJECT_VERSION}
 COMMAND
 ln -s libheartbeat.so.${PROJECT_VERSION} libheartbeat.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
