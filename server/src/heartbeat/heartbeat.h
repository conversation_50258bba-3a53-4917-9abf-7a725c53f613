#ifndef _HEARTBEAT_H_
#define _HEARTBEAT_H_

#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>
#include <memory>
#include <functional>

#include "mqtt_cpp/mqtt_client_cpp.hpp"
#include "logger.hpp"
#include "json/json.h"
#include "commonFunction.h"

/**
 * @brief MQTT心跳发送类
 *
 * 功能特性：
 * 1. 自动连接MQTT服务器，连接失败时每10秒重连
 * 2. 定期发送心跳数据（间隔可配置）
 * 3. 高鲁棒性，除手动停止外持续运行
 * 4. 支持TLS连接（预留接口）
 * 5. 低资源消耗设计
 */
class HeartbeatSender {
public:
    /**
     * @brief 心跳配置结构体
     */
    struct Config {
        std::string server_host;        // MQTT服务器地址
        std::string server_port;        // MQTT服务器端口
        std::string username;           // MQTT用户名
        std::string password;           // MQTT密码
        std::string device_id;          // 设备ID
        std::string topic;              // 发布主题
        int heartbeat_interval_sec;     // 心跳间隔（秒）
        bool enable_tls;                // 是否启用TLS（预留）

        Config() : server_host("localhost"), server_port("1883"),
                  heartbeat_interval_sec(10), enable_tls(false) {}
    };

public:
    /**
     * @brief 构造函数
     * @param config 心跳配置
     */
    explicit HeartbeatSender(const Config& config);

    /**
     * @brief 析构函数
     */
    ~HeartbeatSender();

    /**
     * @brief 启动心跳服务
     * @return true 启动成功，false 启动失败
     */
    bool start();

    /**
     * @brief 停止心跳服务
     */
    void stop();

    /**
     * @brief 检查是否正在运行
     * @return true 正在运行，false 已停止
     */
    bool isRunning() const;

private:
    /**
     * @brief 心跳工作线程主函数
     */
    void heartbeatWorker();

    /**
     * @brief 连接MQTT服务器
     * @return true 连接成功，false 连接失败
     */
    bool connectToServer();

    /**
     * @brief 发送心跳数据
     * @return true 发送成功，false 发送失败
     */
    bool sendHeartbeat();

    /**
     * @brief 发布MQTT消息
     * @param topic 主题
     * @param payload 消息内容
     * @return true 发送成功，false 发送失败
     */
    bool publishMessage(const std::string& topic, const std::string& payload);

    /**
     * @brief 生成心跳JSON数据
     * @return JSON格式的心跳数据字符串
     */
    std::string generateHeartbeatJson();

    /**
     * @brief 断开连接并清理资源
     */
    void disconnect();

private:
    Config m_config;                                    // 配置信息
    std::atomic<bool> m_running;                        // 运行状态标志
    std::atomic<bool> m_connected;                      // 连接状态标志
    std::thread m_worker_thread;                        // 工作线程
    std::mutex m_mutex;                                 // 互斥锁

    boost::asio::io_context m_ioc;                      // ASIO上下文
    std::thread m_ioc_thread;                           // IOC运行线程
    std::shared_ptr<void> m_mqtt_client;                // MQTT客户端（使用void*存储以避免模板复杂性）

    static constexpr int RECONNECT_INTERVAL_SEC = 10;   // 重连间隔（秒）
    static constexpr int CONNECTION_TIMEOUT_SEC = 5;    // 连接超时（秒）
};

#endif // _HEARTBEAT_H_