
#include "heartbeat.h"
// #include "json_utils.h"
#include <chrono>
#include <iostream>

CHeartbeatClient::CHeartbeatClient(const std::string& server_address,
                                   uint16_t port,
                                   const std::string& topic,
                                   const std::string& device_id,
                                   int interval_sec,
                                   int qos)
    : server_address_(server_address),
      port_(port),
      topic_(topic),
      device_id_(device_id),
      interval_sec_(interval_sec),
      qos_(qos),
      running_(false)
{
}

CHeartbeatClient::~CHeartbeatClient() {
    stop();
}

void CHeartbeatClient::start() {
    if (running_) return;

    running_ = true;
    thread_ = std::thread(&CHeartbeatClient::run, this);
}

void CHeartbeatClient::stop() {
    if (!running_) return;

    running_ = false;
    if (thread_.joinable()) thread_.join();

    if (client_ && client_->connected()) {
        client_->disconnect();
    }
}

void CHeartbeatClient::run() {
    boost::asio::io_context ioc;
    client_ = mqtt::make_sync_client(ioc, server_address_, port_);

    client_->set_client_id("heartbeat_client_" + device_id_);
    client_->connect();

    while (running_) {
        std::string payload = create_heartbeat_json();
        try {
            client_->publish_at(topic_, payload, qos_);
        } catch (const std::exception& e) {
            std::cerr << "Publish failed: " << e.what() << std::endl;
        }

        std::this_thread::sleep_for(std::chrono::seconds(interval_sec_));
    }
}

std::string CHeartbeatClient::create_heartbeat_json() {
    Json::Value root;
    root["device_id"] = device_id_;
    root["timestamp"] = static_cast<Json::Int64>(std::chrono::duration_cast<std::chrono::milliseconds>(
                         std::chrono::system_clock::now().time_since_epoch()).count());

    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, root);
}
