#include "heartbeat.h"
#include <iomanip>
#include <sstream>

// 静态常量定义
constexpr int HeartbeatSender::RECONNECT_INTERVAL_SEC;
constexpr int HeartbeatSender::CONNECTION_TIMEOUT_SEC;

HeartbeatSender::HeartbeatSender(const Config& config)
    : m_config(config), m_running(false), m_connected(false) {
    // 验证配置参数
    if (m_config.server_host.empty() || m_config.server_port.empty() ||
        m_config.device_id.empty() || m_config.topic.empty()) {
        ERROR("HeartbeatSender: Invalid configuration parameters");
        throw std::invalid_argument("Invalid configuration parameters");
    }

    if (m_config.heartbeat_interval_sec <= 0) {
        ERROR("HeartbeatSender: Invalid heartbeat interval: {}", m_config.heartbeat_interval_sec);
        throw std::invalid_argument("Invalid heartbeat interval");
    }

    INFO("HeartbeatSender initialized - Host: {}, Port: {}, Device: {}, Topic: {}, Interval: {}s",
         m_config.server_host, m_config.server_port, m_config.device_id,
         m_config.topic, m_config.heartbeat_interval_sec);
}

HeartbeatSender::~HeartbeatSender() {
    stop();
}

bool HeartbeatSender::start() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_running.load()) {
        INFO("HeartbeatSender: Already running");
        return true;
    }

    try {
        m_running.store(true);
        m_connected.store(false);

        // 启动工作线程
        m_worker_thread = std::thread(&HeartbeatSender::heartbeatWorker, this);

        INFO("HeartbeatSender: Started successfully");
        return true;
    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Failed to start - {}", e.what());
        m_running.store(false);
        return false;
    }
}

void HeartbeatSender::stop() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_running.load()) {
        return;
    }

    INFO("HeartbeatSender: Stopping...");
    m_running.store(false);

    // 断开连接
    disconnect();

    // 停止IOC
    if (!m_ioc.stopped()) {
        m_ioc.stop();
    }

    // 等待IOC线程结束
    if (m_ioc_thread.joinable()) {
        m_ioc_thread.join();
    }

    // 等待工作线程结束
    if (m_worker_thread.joinable()) {
        m_worker_thread.join();
    }

    INFO("HeartbeatSender: Stopped");
}

bool HeartbeatSender::isRunning() const {
    return m_running.load();
}

void HeartbeatSender::heartbeatWorker() {
    INFO("HeartbeatSender: Worker thread started");

    auto last_heartbeat_time = std::chrono::steady_clock::now();
    auto last_connect_attempt = std::chrono::steady_clock::now() - std::chrono::seconds(RECONNECT_INTERVAL_SEC);

    while (m_running.load()) {
        auto now = std::chrono::steady_clock::now();

        // 检查连接状态，如果未连接且超过重连间隔，尝试连接
        if (!m_connected.load()) {
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_connect_attempt).count() >= RECONNECT_INTERVAL_SEC) {
                INFO("HeartbeatSender: Attempting to connect to MQTT server...");
                if (connectToServer()) {
                    m_connected.store(true);
                    last_heartbeat_time = now; // 重置心跳时间
                    INFO("HeartbeatSender: Connected to MQTT server successfully");
                } else {
                    ERROR("HeartbeatSender: Failed to connect to MQTT server");
                }
                last_connect_attempt = now;
            }
        }

        // 如果已连接且到了发送心跳的时间，发送心跳
        if (m_connected.load()) {
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_heartbeat_time).count() >= m_config.heartbeat_interval_sec) {
                if (sendHeartbeat()) {
                    last_heartbeat_time = now;
                } else {
                    ERROR("HeartbeatSender: Failed to send heartbeat, marking as disconnected");
                    m_connected.store(false);
                    disconnect();
                }
            }
        }

        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    INFO("HeartbeatSender: Worker thread stopped");
}

bool HeartbeatSender::connectToServer() {
    try {
        // 重置IOC
        if (!m_ioc.stopped()) {
            m_ioc.stop();
        }
        if (m_ioc_thread.joinable()) {
            m_ioc_thread.join();
        }
        m_ioc.restart();

        // 连接状态标志
        std::atomic<bool> connect_result(false);
        std::atomic<bool> connect_finished(false);

        // 创建MQTT客户端并设置回调
        auto client = MQTT_NS::make_async_client(m_ioc, m_config.server_host, m_config.server_port);
        m_mqtt_client = client; // 存储客户端

        // 设置客户端参数
        if (!m_config.username.empty()) {
            client->set_user_name(m_config.username);
        }
        if (!m_config.password.empty()) {
            client->set_password(m_config.password);
        }

        // 生成客户端ID（设备ID + 时间戳确保唯一性）
        std::string client_id = m_config.device_id + "_" + std::to_string(commonFunc::GetMsTime());
        client->set_client_id(client_id);
        client->set_clean_session(true);
        client->set_keep_alive_sec(m_config.heartbeat_interval_sec * 2); // 保活时间设为心跳间隔的2倍

        // 设置连接成功回调
        client->set_connack_handler([&](bool sp, MQTT_NS::connect_return_code connack_return_code) {
            if (connack_return_code == MQTT_NS::connect_return_code::accepted) {
                connect_result.store(true);
                INFO("HeartbeatSender: MQTT connection accepted");
            } else {
                ERROR("HeartbeatSender: MQTT connection rejected - {}",
                      MQTT_NS::connect_return_code_to_str(connack_return_code));
            }
            connect_finished.store(true);
            return true;
        });

        // 设置错误处理回调
        client->set_error_handler([this](MQTT_NS::error_code ec) {
            ERROR("HeartbeatSender: MQTT error - {}", ec.message());
            m_connected.store(false);
        });

        // 设置连接关闭回调
        client->set_close_handler([this]() {
            INFO("HeartbeatSender: MQTT connection closed");
            m_connected.store(false);
        });

        // 异步连接
        client->async_connect([&](MQTT_NS::error_code ec) {
            if (ec) {
                ERROR("HeartbeatSender: MQTT connect error - {}", ec.message());
                connect_finished.store(true);
            }
        });

        // 启动IOC线程
        m_ioc_thread = std::thread([this, client]() {
            try {
                m_ioc.run();
            } catch (const std::exception& e) {
                ERROR("HeartbeatSender: IOC thread exception - {}", e.what());
            }
        });

        // 等待连接结果（带超时）
        auto start_time = std::chrono::steady_clock::now();
        while (!connect_finished.load() && m_running.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            if (std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - start_time).count() >= CONNECTION_TIMEOUT_SEC) {
                ERROR("HeartbeatSender: Connection timeout");
                break;
            }
        }

        return connect_result.load();

    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Exception in connectToServer - {}", e.what());
        return false;
    }
}

bool HeartbeatSender::sendHeartbeat() {
    if (!m_connected.load() || !m_mqtt_client) {
        return false;
    }

    try {
        std::string heartbeat_json = generateHeartbeatJson();
        return publishMessage(m_config.topic, heartbeat_json);
    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Exception in sendHeartbeat - {}", e.what());
        return false;
    }
}

bool HeartbeatSender::publishMessage(const std::string& topic, const std::string& payload) {
    if (!m_connected.load() || !m_mqtt_client) {
        return false;
    }

    try {
        // 将void*转换回实际的客户端类型
        auto client = std::static_pointer_cast<MQTT_NS::async_client<MQTT_NS::tcp_endpoint<boost::asio::ip::tcp::socket, MQTT_NS::strand>>>(m_mqtt_client);

        // 异步发布消息
        std::atomic<bool> publish_result(false);
        std::atomic<bool> publish_finished(false);

        client->async_publish(
            topic,
            payload,
            MQTT_NS::qos::at_least_once,
            [&](MQTT_NS::error_code ec) {
                if (!ec) {
                    publish_result.store(true);
                    INFO("HeartbeatSender: Heartbeat published successfully");
                } else {
                    ERROR("HeartbeatSender: Failed to publish heartbeat - {}", ec.message());
                }
                publish_finished.store(true);
            }
        );

        // 等待发布结果（带超时）
        auto start_time = std::chrono::steady_clock::now();
        while (!publish_finished.load() && m_running.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            if (std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - start_time).count() >= 5) {
                ERROR("HeartbeatSender: Publish timeout");
                break;
            }
        }

        return publish_result.load();

    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Exception in publishMessage - {}", e.what());
        return false;
    }
}

std::string HeartbeatSender::generateHeartbeatJson() {
    try {
        Json::Value heartbeat;

        // 获取当前时间戳（毫秒）
        uint64_t timestamp = commonFunc::GetMsTime();

        // 构建心跳JSON
        heartbeat["timestamp"] = Json::UInt64(timestamp);
        heartbeat["device_id"] = m_config.device_id;

        // 转换为字符串
        Json::StreamWriterBuilder builder;
        builder["indentation"] = ""; // 紧凑格式
        return Json::writeString(builder, heartbeat);

    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Exception in generateHeartbeatJson - {}", e.what());
        return "{}";
    }
}

void HeartbeatSender::disconnect() {
    try {
        if (m_mqtt_client) {
            auto client = std::static_pointer_cast<MQTT_NS::async_client<MQTT_NS::tcp_endpoint<boost::asio::ip::tcp::socket, MQTT_NS::strand>>>(m_mqtt_client);
            client->async_disconnect([](MQTT_NS::error_code ec) {
                if (ec) {
                    ERROR("HeartbeatSender: Disconnect error - {}", ec.message());
                }
            });
            m_mqtt_client.reset();
        }
        m_connected.store(false);
    } catch (const std::exception& e) {
        ERROR("HeartbeatSender: Exception in disconnect - {}", e.what());
    }
}